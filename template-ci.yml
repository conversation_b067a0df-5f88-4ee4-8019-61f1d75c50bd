# 下列变量需在项目中定义
# CI_POD_DEV = xxx-dev
# CI_POD_PRE = xxx-pre
# CI_POD_PROD = xxx-prod
# CI_IMAGE_NAME = project-name

# 使用方式
# include:
#  - project: 'Infrastructure/DevOps/Gitlab-Template'
#    ref: 'master'
#    file: 'vue-ci.yml'

# variables:

#   # 开发、预生产、生产环境编译命令
#   PACKAGE_SCRIPT_DEV: build:dev
#   PACKAGE_SCRIPT_PRE: build:pre
#   PACKAGE_SCRIPT_PROD: build:prod


# Job 默认值
# default:
#   tags:
#     - k8s-v4 # kubernetes

variables:
  DOCKER_IMAGE: "${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_IMAGE_NAME}"

  # 开发、预生产、生产环境编译命令
  PACKAGE_SCRIPT_DEV: build:dev
  PACKAGE_SCRIPT_PRE: build:pre
  PACKAGE_SCRIPT_PROD: build

# 步骤定义
stages:
  - check
  - build
  - tests
  - deploy

# 代码检查
# sonarqube-check:
#   image:
#     name: sonarsource/sonar-scanner-cli:latest
#     entrypoint: [ "" ]
#   stage: check
#   rules:
#     - if: $CI_PIPELINE_SOURCE == "merge_request_event"
#     - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "main"
#   variables:
#     SONAR_USER_HOME: ".sonar"
#     GIT_DEPTH: "0"
#   cache:
#     key: "${CI_PROJECT_NAME}"
#     paths:
#       - .sonar/cache
#   script:
#     - sonar-scanner

# 测试环境
development:build:
  image: docker:stable
  stage: build
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  rules:
    - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "main"
  script:
    - |
      
      # 镜像推送
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "Dockerfile" -t $DOCKER_IMAGE:$TAG_NAME . --build-arg PACKAGE_SCRIPT=$PACKAGE_SCRIPT_DEV
      docker push $DOCKER_IMAGE:$TAG_NAME
      docker tag $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME
      docker push $DOCKER_IMAGE:$ENV_NAME
      docker rmi $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME --force

# 测试发版
development:deploy:
  image:
    name: bitnami/kubectl:latest
    entrypoint: [ '' ]
  stage: deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "main"
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  script:
    - echo "$KUBE_CONFIG" > /.kube/config
    - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$DOCKER_IMAGE:$TAG_NAME -n default
  environment:
    name: Development
    url: $CI_ENVIRONMENT_URL

# 预生产
pre-production:build:
  image: docker:stable
  stage: build
  variables:
    ENV_NAME: pre
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  rules:
    - if: $CI_COMMIT_BRANCH == "pre-production"
  script:
    - |
      
      # 镜像推送
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "Dockerfile" -t $DOCKER_IMAGE:$TAG_NAME . --build-arg PACKAGE_SCRIPT=$PACKAGE_SCRIPT_PRE
      docker push $DOCKER_IMAGE:$TAG_NAME
      docker tag $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME
      docker push $DOCKER_IMAGE:$ENV_NAME
      docker rmi $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME --force

# 预生产发版
pre-production:deploy:
  image:
    name: bitnami/kubectl:latest
    entrypoint: [ '' ]
  stage: deploy
  when: manual # 手动发布
  rules:
    - if: $CI_COMMIT_BRANCH == "pre-production"
  variables:
    ENV_NAME: pre
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  script:
    - echo "$KUBE_CONFIG" > /.kube/config
    - kubectl set image deployment/$CI_POD_PRE $CI_POD_PRE=$DOCKER_IMAGE:$TAG_NAME -n default
  environment:
    name: PreProduction
    url: $CI_ENVIRONMENT_URL


# 生产
production:build:
  image: docker:stable
  stage: build
  variables:
    ENV_NAME: release
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  rules:
    - if: $CI_COMMIT_BRANCH == "production"
  script:
    - |
      
      # 镜像推送
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "Dockerfile" -t $DOCKER_IMAGE:$TAG_NAME . --build-arg PACKAGE_SCRIPT=$PACKAGE_SCRIPT_PROD
      docker push $DOCKER_IMAGE:$TAG_NAME
      docker tag $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME
      docker push $DOCKER_IMAGE:$ENV_NAME
      docker rmi $DOCKER_IMAGE:$TAG_NAME $DOCKER_IMAGE:$ENV_NAME --force

# 生产发版
production:deploy:
  image:
    name: bitnami/kubectl:latest
    entrypoint: [ '' ]
  stage: deploy
  when: manual # 手动发布
  rules:
    - if: $CI_COMMIT_BRANCH == "production"
  variables:
    ENV_NAME: release
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  script:
    - echo "$KUBE_CONFIG" > /.kube/config
    - kubectl set image deployment/$CI_POD_PROD $CI_POD_PROD=$DOCKER_IMAGE:$TAG_NAME -n default
  environment:
    name: Production
    url: $CI_ENVIRONMENT_URL
