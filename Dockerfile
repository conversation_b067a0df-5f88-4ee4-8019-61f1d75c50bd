# 使用 Node.js 20 作为基础镜像
FROM node:20-alpine AS base

# 安装 pnpm
RUN npm install -g pnpm pm2

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 pnpm 相关文件
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# 复制所有子项目的 package.json
COPY client/package.json ./client/
COPY server/package.json ./server/
COPY tests-e2e/package.json ./tests-e2e/

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建参数，用于区分不同环境的构建命令
ARG PACKAGE_SCRIPT=build

# 构建应用
RUN pnpm run ${PACKAGE_SCRIPT}

# 生产阶段
FROM node:20-alpine AS production

# 安装 pm2
RUN npm install -g pm2

# 设置工作目录
WORKDIR /app

# 复制构建产物和必要文件
COPY --from=base /app/server/dist ./server/dist
COPY --from=base /app/server/public ./server/public
COPY --from=base /app/server/package.json ./server/
COPY --from=base /app/server/ecosystem.config.cjs ./server/
COPY --from=base /app/server/node_modules ./server/node_modules

# 设置工作目录到 server
WORKDIR /app/server

# 暴露端口
EXPOSE 8080

# 使用 PM2 启动应用
CMD ["pm2-runtime", "start", "ecosystem.config.cjs", "--env", "prod"]