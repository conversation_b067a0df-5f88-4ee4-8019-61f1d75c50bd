import { createFileRoute } from '@tanstack/react-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { trpc } from '../../lib/trpc'
import { Link } from '@tanstack/react-router'
import { 
  Plus, 
  Coins, 
  Users, 
  Zap, 
  Activity,
  CreditCard,
  Smartphone,
  Settings,
  BarChart3,

  Clock,
  AlertCircle
} from 'lucide-react'

export const Route = createFileRoute('/_authenticated/')({
  component: HomePage,
})

function HomePage() {
  
  // 获取用户的应用列表
  const { data: appsData } = trpc.application.list.useQuery({
    page: 1,
    pageSize: 5, // 只显示前5个应用
  })

  const formatBalance = (balance: string) => {
    return parseFloat(balance).toFixed(2)
  }

  const formatTraffic = (trafficKB: number) => {
    if (trafficKB < 1024) {
      return `${trafficKB} KB`
    } else if (trafficKB < 1024 * 1024) {
      return `${(trafficKB / 1024).toFixed(2)} MB`
    } else {
      return `${(trafficKB / (1024 * 1024)).toFixed(2)} GB`
    }
  }

  // 计算总体数据
  const totalBalance = appsData?.items?.reduce((sum, app) => sum + parseFloat(app.balance), 0) || 0
  const totalApps = appsData?.total || 0
  const totalTraffic = 0

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">工作台</h1>
          <p className="text-muted-foreground mt-1">欢迎回来！</p>
        </div>
        <Button asChild>
          <Link to="/apps/create">
            <Plus className="w-4 h-4 mr-2" />
            创建应用
          </Link>
        </Button>
      </div>

      {/* 数据概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {totalBalance.toFixed(2)} 蚁贝
                </div>
                <div className="text-sm text-blue-600 dark:text-blue-400 flex items-center gap-1">
                  <Coins className="h-4 w-4" />
                  总余额
                </div>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Coins className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-green-700">
                  {totalApps}
                </div>
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <Smartphone className="h-4 w-4" />
                  应用总数
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-yellow-700">
                  {formatTraffic(totalTraffic)}
                </div>
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <Zap className="h-4 w-4" />
                  总流量使用
                </div>
              </div>
              <div className="bg-yellow-100 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-purple-700">
                  0.00
                </div>
                <div className="text-sm text-muted-foreground flex items-center gap-1">
                  <Activity className="h-4 w-4" />
                  本月消费
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Activity className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 快速导航 */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              快速操作
            </CardTitle>
            <CardDescription>常用功能快速入口</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button asChild className="w-full justify-start">
              <Link to="/apps">
                <Smartphone className="w-4 h-4 mr-2" />
                应用管理
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link to="/apps/create">
                <Plus className="w-4 h-4 mr-2" />
                创建新应用
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link to="/recharge">
                <CreditCard className="w-4 h-4 mr-2" />
                充值测试
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start">
              <Link to="/orders">
                <Clock className="w-4 h-4 mr-2" />
                订单管理
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* 我的应用 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              我的应用
            </CardTitle>
            <CardDescription>最近创建的应用</CardDescription>
          </CardHeader>
          <CardContent>
            {appsData?.items && appsData.items.length > 0 ? (
              <div className="space-y-3">
                {appsData.items.map((app) => (
                  <div key={app.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="bg-primary/10 p-2 rounded-lg">
                        <Smartphone className="h-4 w-4 text-primary" />
                      </div>
                      <div>
                        <div className="font-medium">{app.name}</div>
                        <div className="text-sm text-muted-foreground">
                          余额: {formatBalance(app.balance)} 蚁贝
                        </div>
                      </div>
                    </div>
                    <Button asChild variant="ghost" size="sm">
                      <Link to="/apps/$id" params={{ id: app.id }}>
                        查看详情
                      </Link>
                    </Button>
                  </div>
                ))}
                {appsData.total > 5 && (
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/apps">
                      查看全部 {appsData.total} 个应用
                    </Link>
                  </Button>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <Smartphone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground mb-4">还没有创建任何应用</p>
                <Button asChild>
                  <Link to="/apps/create">
                    <Plus className="w-4 h-4 mr-2" />
                    创建第一个应用
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 最近活动 - 暂时显示占位内容 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            最近交易记录
          </CardTitle>
          <CardDescription>最近的蚁贝交易活动</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">暂无交易记录</p>
            <p className="text-sm text-muted-foreground mt-1">
              交易记录功能正在开发中，请在应用详情页查看具体应用的交易记录
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 余额警告 - 基于当前数据显示低余额应用 */}
      {appsData?.items && appsData.items.some(app => parseFloat(app.balance) < 10) && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <AlertCircle className="h-5 w-5" />
              余额提醒
            </CardTitle>
            <CardDescription className="text-orange-600">
              以下应用余额不足，建议及时充值
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {appsData.items
                .filter(app => parseFloat(app.balance) < 10)
                .map((app) => (
                  <div key={app.id} className="flex items-center justify-between p-2 bg-white rounded border">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-orange-500" />
                      <span>{app.name}</span>
                      <Badge variant="outline" className="text-orange-600">
                        {formatBalance(app.balance)} 蚁贝
                      </Badge>
                    </div>
                    <Button asChild variant="outline" size="sm">
                      <Link to="/recharge">
                        立即充值
                      </Link>
                    </Button>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 