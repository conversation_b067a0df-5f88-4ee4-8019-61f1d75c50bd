import { db } from '@/db'
import type { Application } from '@/db/application'
import { applications } from '@/db/application'
import { sendWebhookNotification } from '@/lib/oauth2'
import { getSessionToken } from '@/lib/session-token'
import { and, eq } from 'drizzle-orm'
import type { FastifyInstance } from 'fastify'

declare module 'fastify' {
  interface FastifyRequest {
    application: Application
  }
}

export const crawlerWebhookRoutes = async (app: FastifyInstance) => {
  app.addHook('preHandler', async (request, _reply) => {
    const authHeader = request.headers.authorization
    const sessionToken = await getSessionToken(authHeader ?? '')
    // 获取应用信息
    const existingApp = await db.query.applications.findFirst({
      where: and(eq(applications.id, sessionToken.applicationId), eq(applications.userId, sessionToken.userId)),
    })
    if (!existingApp) {
      return _reply.status(401).send({
        success: false,
        message: 'Unauthorized',
      })
    }
    request.application = existingApp
  })
  app.post('/crawler/webhook', async (request, reply) => {
    const { application } = request
    const { webhookUrl } = application
    if (!webhookUrl) {
      return reply.status(400).send({
        success: false,
        message: 'Webhook URL not found',
      })
    } else {
      await sendWebhookNotification(webhookUrl, 'web', request.body)
    }
    return reply.send()
  })
}
