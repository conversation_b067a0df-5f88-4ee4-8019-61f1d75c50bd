import type { FastifyInstance } from 'fastify'
import { oauth2Routes } from './oauth2'
import { xiaohongshuWebhookRoutes } from './webhook/xiaohongshu'
import { crawlerWebhookRoutes } from './webhook/crawler'
import { crawlerProxyRoutes } from './crawler-porxy'

const routes = (app: FastifyInstance) => {
  app.register(oauth2Routes, { prefix: '/oauth2' })
  app.register(xiaohongshuWebhookRoutes, { prefix: '/webhook' })
  app.register(crawlerWebhookRoutes, { prefix: '/webhook' })
  app.register(crawlerProxyRoutes, { prefix: '/web' })
}

export default routes
