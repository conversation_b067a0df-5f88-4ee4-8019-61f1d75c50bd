import { z } from 'zod'
import { router, publicProcedure } from '../trpc'

import { TRPCError } from '@trpc/server'
import { LoginSchema, CreateUserSchema } from '../db/schema'
import { sendSMSCode, loginOrRegisterWithPhone, loginWithPassword, registerWithPassword } from '../lib/auth'
import { generateToken } from '../lib/jwt'
import type { JWTPayload } from '../lib/jwt'
import { isdev } from '@/env'
import { protectedProcedure } from '@/procedure'

export const PhoneLoginSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号'),
  code: z.string().length(6, '验证码必须是6位'),
})

export const SendSmsSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号'),
  type: z.enum(['login', 'register', 'reset_password']).default('login'),
})

export const authRouter = router({
  // 发送短信验证码
  sendSms: publicProcedure.input(SendSmsSchema).mutation(async ({ input }) => {
    try {
      const result = await sendSMSCode(input.phone, input.type)

      if (!result.success) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: '短信发送失败',
        })
      }

      return {
        message: '验证码发送成功',
        code: isdev ? result.code : undefined,
      }
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : '发送失败',
      })
    }
  }),

  // 手机号验证码登录/注册
  phoneLogin: publicProcedure.input(PhoneLoginSchema).mutation(async ({ input }) => {
    try {
      const result = await loginOrRegisterWithPhone(input.phone, input.code)

      return {
        message: result.isNewUser ? '注册成功' : '登录成功',
        token: result.token,
        isNewUser: result.isNewUser,
      }
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : '登录失败',
      })
    }
  }),

  // 账号密码登录
  login: publicProcedure.input(LoginSchema).mutation(async ({ input }) => {
    try {
      const emailOrPhone = input.email ?? input.phone ?? ''
      const result = await loginWithPassword(emailOrPhone, input.password)
      return {
        message: '登录成功',
        token: result.token,
      }
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : '登录失败',
      })
    }
  }),

  // 账号密码注册
  register: publicProcedure
    .input(
      CreateUserSchema.extend({
        password: z.string().min(6, '密码至少6位'),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const result = await registerWithPassword(input)

        // 设置cookie
        ctx.res.setCookie('auth-token', result.token, {
          httpOnly: true,
          secure: !isdev,
          sameSite: 'lax',
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
        })

        return {
          message: '注册成功',
          token: result.token,
        }
      } catch (error) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : '注册失败',
        })
      }
    }),

  // 退出登录
  logout: protectedProcedure.mutation(({ ctx }) => {
    ctx.res.setCookie('auth-token', '', {
      httpOnly: true,
      secure: !isdev,
      sameSite: 'lax',
      maxAge: 0, // 立即过期
    })

    return { message: '退出成功' }
  }),

  // 刷新token
  refresh: protectedProcedure.mutation(({ ctx }) => {
    // 重新生成token
    const jwtPayload: JWTPayload = {
      userId: ctx.user.id,
      phone: ctx.user.phone ?? '',
    }

    const newToken = generateToken(jwtPayload)

    // 设置新的cookie
    ctx.res.setCookie('auth-token', newToken, {
      httpOnly: true,
      secure: !isdev,
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    })

    return {
      message: '刷新成功',
      token: newToken,
    }
  }),
})
